#!/usr/bin/env python3
"""
LLDB-DAP Fast Launch Implementation Validation

This script validates that the fast launch implementation is correctly
integrated across all required files and follows LLVM coding standards.
"""

import os
import sys
import re

def test_dap_header_changes():
    """Test that DAP.h has the required fast launch declarations."""
    print("Testing DAP.h changes...")

    dap_h_path = "../../../../../tools/lldb-dap/DAP.h"
    if not os.path.exists(dap_h_path):
        print(f"FAIL: DAP.h not found at {dap_h_path}")
        return False

    with open(dap_h_path, 'r') as f:
        content = f.read()

    required_declarations = [
        "CreateTarget(lldb::SBError &error, bool fast_launch = false)",
        "LoadSymbolsAsync()",
        "IsFastLaunchMode()",
        "ShouldDeferSymbolLoading()",
        "ShouldUseLazyPluginLoading()",
        "GetLaunchTimeoutMs()",
        "StartPerformanceTiming",
        "EndPerformanceTiming"
    ]

    missing = []
    for decl in required_declarations:
        if decl not in content:
            missing.append(decl)

    if missing:
        print(f"FAIL: Missing declarations in DAP.h: {missing}")
        return False

    print("PASS: DAP.h contains all required fast launch declarations")
    return True

def test_dap_cpp_implementation():
    """Test that DAP.cpp has the required fast launch implementations."""
    print("Testing DAP.cpp implementation...")

    dap_cpp_path = "../../../../../tools/lldb-dap/DAP.cpp"
    if not os.path.exists(dap_cpp_path):
        print(f"FAIL: DAP.cpp not found at {dap_cpp_path}")
        return False

    with open(dap_cpp_path, 'r') as f:
        content = f.read()

    required_implementations = [
        "bool DAP::IsFastLaunchMode()",
        "bool DAP::ShouldDeferSymbolLoading()",
        "bool DAP::ShouldUseLazyPluginLoading()",
        "uint32_t DAP::GetLaunchTimeoutMs()",
        "void DAP::StartPerformanceTiming",
        "uint32_t DAP::EndPerformanceTiming",
        "void DAP::LoadSymbolsAsync()",
        "symbols.load-on-demand true",
        "target.preload-symbols false"
    ]

    missing = []
    for impl in required_implementations:
        if impl not in content:
            missing.append(impl)

    if missing:
        print(f"FAIL: Missing implementations in DAP.cpp: {missing}")
        return False

    print("PASS: DAP.cpp contains all required fast launch implementations")
    return True

def test_launch_request_handler():
    """Test that LaunchRequestHandler has fast launch integration."""
    print("Testing LaunchRequestHandler changes...")

    handler_path = "../../../../../tools/lldb-dap/Handler/LaunchRequestHandler.cpp"
    if not os.path.exists(handler_path):
        print(f"FAIL: LaunchRequestHandler.cpp not found at {handler_path}")
        return False

    with open(handler_path, 'r') as f:
        content = f.read()

    required_changes = [
        "IsFastLaunchMode()",
        "CreateTarget",
        "fast_launch"
    ]

    missing = []
    for change in required_changes:
        if change not in content:
            missing.append(change)

    if missing:
        print(f"FAIL: Missing changes in LaunchRequestHandler.cpp: {missing}")
        return False

    print("PASS: LaunchRequestHandler.cpp contains all required fast launch changes")
    return True

def test_protocol_configuration():
    """Test that protocol configuration supports fast launch options."""
    print("Testing protocol configuration...")

    protocol_h_path = "../../../../../tools/lldb-dap/Protocol/ProtocolRequests.h"
    if not os.path.exists(protocol_h_path):
        print(f"FAIL: ProtocolRequests.h not found at {protocol_h_path}")
        return False

    with open(protocol_h_path, 'r') as f:
        content = f.read()

    required_options = [
        "fastLaunchMode",
        "deferSymbolLoading",
        "lazyPluginLoading",
        "launchTimeoutMs",
        "debuginfodTimeoutMs"
    ]

    missing = []
    for option in required_options:
        if option not in content:
            missing.append(option)

    if missing:
        print(f"FAIL: Missing configuration options: {missing}")
        return False

    print("PASS: Protocol configuration supports all fast launch options")
    return True

def test_llvm_compliance():
    """Test that implementation follows LLVM coding standards."""
    print("Testing LLVM compliance...")

    files_to_check = [
        "../../../../../tools/lldb-dap/DAP.cpp",
        "../../../../../tools/lldb-dap/Handler/LaunchRequestHandler.cpp"
    ]

    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"FAIL: File not found: {file_path}")
            return False

        with open(file_path, 'r') as f:
            content = f.read()

        # Check for proper LLVM error handling
        if "lldb::SBError" not in content and "DAP.cpp" in file_path:
            print(f"FAIL: {file_path}: Missing proper LLVM error handling")
            return False

        print(f"PASS: {file_path}: Uses proper LLVM error handling")

    print("PASS: Implementation follows LLVM coding standards")
    return True

def test_documentation():
    """Test that documentation exists and contains fast launch information."""
    print("Testing documentation...")

    release_notes_path = "../../../../../../llvm/docs/ReleaseNotes.md"
    if not os.path.exists(release_notes_path):
        print(f"FAIL: ReleaseNotes.md not found at {release_notes_path}")
        return False

    with open(release_notes_path, 'r') as f:
        content = f.read()

    if "fast launch" not in content.lower() and "fastlaunch" not in content.lower():
        print("FAIL: Release notes missing fast launch documentation")
        return False

    print("PASS: Documentation exists and contains fast launch information")
    return True

def main():
    """Run all validation tests."""
    print("LLDB-DAP Fast Launch Implementation Validation")
    print("=" * 60)

    tests = [
        test_dap_header_changes,
        test_dap_cpp_implementation,
        test_launch_request_handler,
        test_protocol_configuration,
        test_llvm_compliance,
        test_documentation
    ]

    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"ERROR: {test.__name__} failed with exception: {e}")
            print()

    print("=" * 60)
    print(f"Validation Results: {passed}/{len(tests)} tests passed")

    if passed == len(tests):
        print("All validation tests passed!")
        print("Implementation Status:")
        print("   - Code changes implemented correctly")
        print("   - LLVM compliance verified")
        print("   - Configuration options added")
        print("   - Documentation created")
        print("   - Fast launch architecture complete")
        print("Ready for code review and integration!")
        return True
    else:
        print("Some validation tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
