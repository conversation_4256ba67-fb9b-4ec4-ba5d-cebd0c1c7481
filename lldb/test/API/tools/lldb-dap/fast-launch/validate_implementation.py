#!/usr/bin/env python3
"""
LLDB-DAP Fast Launch Implementation Validation

This script validates that the fast launch implementation is correctly
integrated across all required files and follows LLVM coding standards.
"""

import os
import sys
import re

def test_dap_header_changes():
    """Test that DAP.h has the required fast launch declarations."""
    print("Testing DAP.h changes...")

    dap_h_path = "../../../../../tools/lldb-dap/DAP.h"
    if not os.path.exists(dap_h_path):
        print(f"FAIL: DAP.h not found at {dap_h_path}")
        return False

    with open(dap_h_path, 'r') as f:
        content = f.read()

    required_declarations = [
        "CreateTarget(lldb::SBError &error, bool fast_launch = false)",
        "LoadSymbolsAsync()",
        "IsFastLaunchMode()",
        "ShouldDeferSymbolLoading()",
        "ShouldUseLazyPluginLoading()",
        "GetLaunchTimeoutMs()",
        "StartPerformanceTiming",
        "EndPerformanceTiming"
    ]

    missing = []
    for decl in required_declarations:
        if decl not in content:
            missing.append(decl)

    if missing:
        print(f"FAIL: Missing declarations in DAP.h: {missing}")
        return False

    print("PASS: DAP.h contains all required fast launch declarations")
    return True

def test_dap_cpp_implementation():
    """Test that DAP.cpp has the required fast launch implementations."""
    print("\n🔍 Testing DAP.cpp implementation...")

    dap_cpp_path = "../../../../../tools/lldb-dap/DAP.cpp"
    if not os.path.exists(dap_cpp_path):
        print(f"❌ DAP.cpp not found at {dap_cpp_path}")
        return False

    with open(dap_cpp_path, 'r') as f:
        content = f.read()

    required_implementations = [
        "bool DAP::IsFastLaunchMode()",
        "bool DAP::ShouldDeferSymbolLoading()",
        "bool DAP::ShouldUseLazyPluginLoading()",
        "uint32_t DAP::GetLaunchTimeoutMs()",
        "void DAP::StartPerformanceTiming",
        "uint32_t DAP::EndPerformanceTiming",
        "void DAP::LoadSymbolsAsync()",
        "symbols.load-on-demand true",
        "target.preload-symbols false"
    ]

    missing = []
    for impl in required_implementations:
        if impl not in content:
            missing.append(impl)

    if missing:
        print(f"❌ Missing implementations in DAP.cpp: {missing}")
        return False

    print("✅ DAP.cpp contains all required fast launch implementations")
    return True

def test_launch_handler_changes():
    """Test that LaunchRequestHandler uses fast launch mode."""
    print("\n🔍 Testing LaunchRequestHandler changes...")

    handler_path = "../../../../../tools/lldb-dap/Handler/LaunchRequestHandler.cpp"
    if not os.path.exists(handler_path):
        print(f"❌ LaunchRequestHandler.cpp not found at {handler_path}")
        return False

    with open(handler_path, 'r') as f:
        content = f.read()

    required_changes = [
        "bool fast_launch",
        "dap.CreateTarget(error, fast_launch)",
        "dap.LoadSymbolsAsync()",
        "IsFastLaunchMode()",
        "ShouldDeferSymbolLoading()"
    ]

    missing = []
    for change in required_changes:
        if change not in content:
            missing.append(change)

    if missing:
        print(f"❌ Missing changes in LaunchRequestHandler.cpp: {missing}")
        return False

    print("✅ LaunchRequestHandler.cpp contains all required fast launch changes")
    return True

def test_protocol_configuration():
    """Test that protocol configuration supports fast launch options."""
    print("\n🔍 Testing protocol configuration...")

    # Test ProtocolRequests.h
    header_path = "../../../../../tools/lldb-dap/Protocol/ProtocolRequests.h"
    if not os.path.exists(header_path):
        print(f"❌ ProtocolRequests.h not found at {header_path}")
        return False

    with open(header_path, 'r') as f:
        header_content = f.read()

    # Test ProtocolRequests.cpp
    cpp_path = "../../../../../tools/lldb-dap/Protocol/ProtocolRequests.cpp"
    if not os.path.exists(cpp_path):
        print(f"❌ ProtocolRequests.cpp not found at {cpp_path}")
        return False

    with open(cpp_path, 'r') as f:
        cpp_content = f.read()

    required_config_fields = [
        "fastLaunchMode",
        "deferSymbolLoading",
        "lazyPluginLoading",
        "launchTimeoutMs"
    ]

    missing_header = []
    missing_cpp = []

    for field in required_config_fields:
        if f"std::optional<bool> {field}" not in header_content and f"std::optional<uint32_t> {field}" not in header_content:
            missing_header.append(field)
        if f'mapOptional("{field}"' not in cpp_content:
            missing_cpp.append(field)

    if missing_header:
        print(f"❌ Missing configuration fields in ProtocolRequests.h: {missing_header}")
        return False

    if missing_cpp:
        print(f"❌ Missing configuration parsing in ProtocolRequests.cpp: {missing_cpp}")
        return False

    print("✅ Protocol configuration supports all fast launch options")
    return True

def test_llvm_compliance():
    """Test that implementation follows LLVM coding standards."""
    print("\n🔍 Testing LLVM compliance...")

    files_to_check = [
        "../../../../../tools/lldb-dap/DAP.cpp",
        "../../../../../tools/lldb-dap/Handler/LaunchRequestHandler.cpp"
    ]

    violations = []

    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue

        with open(file_path, 'r') as f:
            content = f.read()

        # Check for C++ exceptions (LLVM policy violation)
        # Look for actual try/catch/throw statements, not just the words in comments
        if re.search(r'^\s*(try\s*\{|catch\s*\(|throw\s+)', content, re.MULTILINE):
            violations.append(f"{file_path}: Contains C++ exceptions")

        # Check for proper error handling patterns
        if "llvm::Error" in content or "lldb::SBError" in content:
            print(f"✅ {os.path.basename(file_path)}: Uses proper LLVM error handling")

    if violations:
        print(f"❌ LLVM compliance violations: {violations}")
        return False

    print("✅ Implementation follows LLVM coding standards")
    return True

def test_documentation_exists():
    """Test that documentation has been created."""
    print("\n🔍 Testing documentation...")

    doc_files = [
        "../../../../../docs/resources/dap-fast-launch.md",
        "../../../../../../llvm/docs/ReleaseNotes.md"
    ]

    missing_docs = []
    for doc_file in doc_files:
        if not os.path.exists(doc_file):
            missing_docs.append(doc_file)
        else:
            with open(doc_file, 'r') as f:
                content = f.read()
                if "fast launch" not in content.lower() and "fastlaunchmode" not in content.lower():
                    missing_docs.append(f"{doc_file} (missing fast launch content)")

    if missing_docs:
        print(f"❌ Missing or incomplete documentation: {missing_docs}")
        return False

    print("✅ Documentation exists and contains fast launch information")
    return True

def main():
    """Run all validation tests."""
    print("🚀 LLDB-DAP Fast Launch Implementation Validation")
    print("=" * 60)

    tests = [
        test_dap_header_changes,
        test_dap_cpp_implementation,
        test_launch_handler_changes,
        test_protocol_configuration,
        test_llvm_compliance,
        test_documentation_exists
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} failed")
        except Exception as e:
            print(f"❌ {test.__name__} failed with exception: {e}")

    print("\n" + "=" * 60)
    print(f"📊 Validation Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All validation tests passed!")
        print("\n📋 Implementation Status:")
        print("   ✅ Code changes implemented correctly")
        print("   ✅ LLVM compliance verified")
        print("   ✅ Configuration options added")
        print("   ✅ Documentation created")
        print("   ✅ Fast launch architecture complete")
        print("\n🚀 Ready for code review and integration!")
        return True
    else:
        print(f"❌ {total - passed} validation tests failed")
        print("Please review and fix the failing components.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
