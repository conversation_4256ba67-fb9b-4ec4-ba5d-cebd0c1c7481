"""
Test lldb-dap fast launch mode functionality and performance optimizations.
"""

import dap_server
from lldbsuite.test.decorators import *
from lldbsuite.test.lldbtest import *
from lldbsuite.test import lldbutil
import lldbdap_testcase
import time
import os


class TestDAP_fastLaunch(lldbdap_testcase.DAPTestCaseBase):

    @skipIfWindows  # Skip on Windows due to different symbol loading behavior
    def test_fast_launch_configuration(self):
        """
        Test that fast launch mode configuration options work correctly.
        """
        program = self.getBuildArtifact("a.out")
        self.build_and_launch(
            program,
            fastLaunchMode=True,
            deferSymbolLoading=True,
            lazyPluginLoading=True,
            launchTimeoutMs=5000,
        )

        # Verify the target was created successfully
        self.assertTrue(self.dap_server.target.IsValid())

        # Test that we can set breakpoints (symbol loading should work on-demand)
        source = "main.cpp"
        breakpoint_line = line_number(source, "// Set breakpoint here")
        lines = [breakpoint_line]
        breakpoint_ids = self.set_source_breakpoints(source, lines)
        self.assertEqual(len(breakpoint_ids), 1)

        # Continue and verify we hit the breakpoint
        self.continue_to_next_stop()
        self.verify_stop_reason_breakpoint(breakpoint_ids[0])

    def test_fast_launch_debugging_functionality(self):
        """
        Test that fast launch mode preserves debugging functionality.
        """
        program = self.getBuildArtifact("a.out")

        # Launch with fast mode enabled
        self.build_and_launch(
            program,
            stopOnEntry=False,
            fastLaunchMode=True,
            deferSymbolLoading=True,
        )

        source = "main.cpp"
        breakpoint_line = line_number(source, "// Set breakpoint here")

        # Set breakpoint - this should trigger on-demand symbol loading
        breakpoint_ids = self.set_source_breakpoints(source, [breakpoint_line])
        self.assertEqual(len(breakpoint_ids), 1)

        # Continue and verify we hit the breakpoint
        self.continue_to_next_stop()
        self.verify_stop_reason_breakpoint(breakpoint_ids[0])

        # Verify stack trace works (requires symbols)
        frames = self.get_stackFrames()
        self.assertGreater(len(frames), 0)

        # Verify variable inspection works
        frame = frames[0]
        self.assertTrue("id" in frame)
        scopes = self.get_scopes(frame["id"])
        self.assertGreater(len(scopes), 0)

    def test_network_symbol_optimization(self):
        """
        Test that network symbol optimization settings work correctly.
        """
        program = self.getBuildArtifact("a.out")

        # Test with network symbols disabled
        self.build_and_launch(
            program,
            stopOnEntry=True,
            fastLaunchMode=True,
            disableNetworkSymbols=True,
            debuginfodTimeoutMs=1000,
        )

        # Verify the target was created successfully
        self.assertTrue(self.dap_server.target.IsValid())

        # Test basic debugging functionality still works
        source = "main.cpp"
        breakpoint_line = line_number(source, "// Set breakpoint here")
        lines = [breakpoint_line]
        breakpoint_ids = self.set_source_breakpoints(source, lines)
        self.assertEqual(len(breakpoint_ids), 1)

        # Continue and verify we hit the breakpoint
        self.continue_to_next_stop()
        self.verify_stop_reason_breakpoint(breakpoint_ids[0])
