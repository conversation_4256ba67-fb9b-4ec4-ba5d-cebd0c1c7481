# LLDB-DAP Fast Launch Mode - Performance Context

## Overview

The fast launch mode in lldb-dap provides performance optimizations that are **context-dependent**. The benefits vary significantly based on project characteristics, network conditions, and symbol loading requirements.

## When Fast Launch Mode Provides Significant Benefits

### 1. Network Symbol Loading Scenarios
- **Debuginfod servers**: When DEBUGINFOD_URLS is configured
- **Remote symbol servers**: Network-based symbol resolution
- **Slow network conditions**: High latency or unreliable connections
- **Corporate networks**: Proxy or firewall-restricted environments

**Expected improvement**: Substantial reduction in startup time by moving network operations to background

### 2. Large Projects with Extensive Debug Information
- **Complex C++ projects**: Heavy template usage, large symbol tables
- **Projects with many dependencies**: Multiple shared libraries
- **Debug builds**: Full debug information (-g flag)
- **Large executables**: >100MB binaries with extensive symbols

**Expected improvement**: Faster target creation by deferring symbol loading

### 3. Development Workflows
- **Rapid iteration**: Frequent debug session restarts
- **CI/CD environments**: Automated testing with debugging
- **Remote debugging**: Network-attached targets

## When Fast Launch Mode Provides Minimal Benefits

### 1. Simple Local Projects
- **Small executables**: <10MB binaries
- **Minimal dependencies**: Few or no shared libraries
- **Local debugging**: No network symbol loading
- **Release builds**: Stripped or minimal debug information

**Expected improvement**: Minimal, as there's little to optimize

### 2. Already Optimized Environments
- **Local symbol caches**: Symbols already cached locally
- **Fast storage**: NVMe SSDs with fast I/O
- **Offline environments**: No network symbol services configured

## Configuration Guidelines

### For Maximum Performance Benefits
```json
{
  "fastLaunchMode": true,
  "deferSymbolLoading": true,
  "lazyPluginLoading": true,
  "launchTimeoutMs": 1000,
  "debuginfodTimeoutMs": 2000,
  "disableNetworkSymbols": false
}
```

### For Offline/Local Development
```json
{
  "fastLaunchMode": true,
  "deferSymbolLoading": true,
  "disableNetworkSymbols": true
}
```

### For Network-Constrained Environments
```json
{
  "fastLaunchMode": true,
  "debuginfodTimeoutMs": 1000,
  "symbolServerTimeoutMs": 1000,
  "disableNetworkSymbols": true
}
```

## Performance Measurement

Performance improvements are measured relative to the specific environment and project characteristics. The implementation includes timing infrastructure to measure actual performance in your specific context.

### Factors Affecting Performance
1. **Project size and complexity**
2. **Network conditions and symbol server availability**
3. **Storage speed and symbol cache state**
4. **System resources and concurrent processes**

## Technical Implementation

Fast launch mode works by:
1. **Deferring symbol loading**: Uses LLDB's on-demand loading
2. **Background network operations**: Moves network symbol loading off critical path
3. **Reduced timeouts**: Faster failure detection for unavailable services
4. **Lazy plugin loading**: Defers non-essential plugin initialization

All optimizations maintain full debugging functionality through LLDB's existing on-demand loading mechanisms.
